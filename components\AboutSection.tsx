import { ImageWithFallback } from './figma/ImageWithFallback';

export function AboutSection() {
  return (
    <section id="about" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl text-gray-900 mb-6">About Us</h2>
          <div className="w-24 h-1 bg-amber-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            With over 15 years of experience, we bring passion, creativity, and excellence to every event we cater.
          </p>
        </div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <div>
            <h3 className="text-3xl mb-6 text-gray-900">Our Story</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Founded in 2008 by Chef <PERSON>, Élégant Catering began as a small family business with a big dream: 
              to create unforgettable culinary experiences that bring people together. What started in a humble kitchen has 
              grown into one of the city's most trusted catering companies.
            </p>
            <p className="text-gray-600 mb-6 leading-relaxed">
              We believe that food is more than sustenance—it's a way to celebrate life's most precious moments. 
              Every dish we create is crafted with the finest ingredients, attention to detail, and love for the culinary arts.
            </p>
            <div className="grid grid-cols-2 gap-6 mt-8">
              <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                <div className="text-2xl text-amber-600 mb-2">500+</div>
                <div className="text-gray-600">Events</div>
              </div>
              <div className="text-center p-4 bg-white rounded-lg shadow-sm">
                <div className="text-2xl text-amber-600 mb-2">15+</div>
                <div className="text-gray-600">Years</div>
              </div>
            </div>
          </div>
          <div className="relative">
            <ImageWithFallback
              src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
              alt="Our kitchen and team"
              className="rounded-lg shadow-xl"
            />
          </div>
        </div>

        {/* Chef Profiles */}
        <div>
          <h3 className="text-3xl text-center mb-12 text-gray-900">Meet Our Chefs</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Chef 1 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=984&q=80"
                alt="Chef Maria Rodriguez"
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <h4 className="text-xl mb-2 text-gray-900">Chef Maria Rodriguez</h4>
                <p className="text-amber-600 mb-4">Executive Chef & Founder</p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  With 20+ years in fine dining, Maria brings international flavors and innovative techniques to every event.
                </p>
              </div>
            </div>

            {/* Chef 2 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1607631568010-a87245c0daf8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80"
                alt="Chef David Kim"
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <h4 className="text-xl mb-2 text-gray-900">Chef David Kim</h4>
                <p className="text-amber-600 mb-4">Pastry Chef</p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  David's artistic desserts are works of art that perfectly complement our savory offerings.
                </p>
              </div>
            </div>

            {/* Chef 3 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1559339352-11d035aa65de?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80"
                alt="Chef Sofia Martinez"
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <h4 className="text-xl mb-2 text-gray-900">Chef Sofia Martinez</h4>
                <p className="text-amber-600 mb-4">Sous Chef</p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Sofia specializes in Mediterranean cuisine and ensures every dish meets our high standards.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Values */}
        <div className="mt-20">
          <h3 className="text-3xl text-center mb-12 text-gray-900">Our Values</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌟</span>
              </div>
              <h4 className="text-xl mb-4 text-gray-900">Excellence</h4>
              <p className="text-gray-600">
                We strive for perfection in every dish, every service, and every moment of your event.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💚</span>
              </div>
              <h4 className="text-xl mb-4 text-gray-900">Sustainability</h4>
              <p className="text-gray-600">
                We source locally and sustainably, supporting our community and protecting our environment.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤝</span>
              </div>
              <h4 className="text-xl mb-4 text-gray-900">Partnership</h4>
              <p className="text-gray-600">
                We work closely with you to understand your vision and bring it to life perfectly.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}