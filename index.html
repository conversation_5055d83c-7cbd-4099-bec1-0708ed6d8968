<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Élégant Catering - Premium Catering Services</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --font-size: 14px;
            --background: #ffffff;
            --foreground: oklch(0.145 0 0);
            --card: #ffffff;
            --card-foreground: oklch(0.145 0 0);
            --primary: #030213;
            --primary-foreground: oklch(1 0 0);
            --secondary: oklch(0.95 0.0058 264.53);
            --secondary-foreground: #030213;
            --muted: #ececf0;
            --muted-foreground: #717182;
            --accent: #e9ebef;
            --accent-foreground: #030213;
            --destructive: #d4183d;
            --destructive-foreground: #ffffff;
            --border: rgba(0, 0, 0, 0.1);
            --input: transparent;
            --input-background: #f3f3f5;
            --font-weight-medium: 500;
            --font-weight-normal: 400;
            --radius: 0.625rem;
        }

        * {
            box-sizing: border-box;
        }

        html {
            font-size: var(--font-size);
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background: var(--background);
            color: var(--foreground);
            line-height: 1.6;
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        .animate-bounce {
            animation: bounce 2s infinite;
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Custom utility classes */
        .text-shadow {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .backdrop-blur {
            backdrop-filter: blur(10px);
        }

        .aspect-ratio-square {
            aspect-ratio: 1 / 1;
        }

        .aspect-ratio-16-9 {
            aspect-ratio: 16 / 9;
        }

        .gradient-overlay {
            background: linear-gradient(135deg, rgba(217, 119, 6, 0.9) 0%, rgba(180, 83, 9, 0.9) 100%);
        }

        /* Navigation styles */
        .nav-sticky {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            transition: all 0.3s ease;
        }

        .nav-scrolled {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* Button styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            text-decoration: none;
            font-size: 1rem;
        }

        .btn-primary {
            background-color: #d97706;
            color: white;
        }

        .btn-primary:hover {
            background-color: #b45309;
            transform: scale(1.05);
        }

        .btn-outline {
            border: 2px solid #d97706;
            color: #d97706;
            background: transparent;
        }

        .btn-outline:hover {
            background-color: #d97706;
            color: white;
        }

        .btn-white {
            border: 2px solid white;
            color: white;
            background: transparent;
        }

        .btn-white:hover {
            background-color: white;
            color: #374151;
        }

        /* Card styles */
        .card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        /* Form styles */
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #d97706;
            box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
        }

        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            resize: vertical;
            min-height: 120px;
            transition: border-color 0.2s ease;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #d97706;
            box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            background: white;
            cursor: pointer;
            transition: border-color 0.2s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: #d97706;
            box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            max-width: 90vw;
            max-height: 90vh;
            position: relative;
        }

        .modal-close {
            position: absolute;
            top: -3rem;
            right: 0;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .modal-close:hover {
            color: #d97706;
        }

        /* Hero section styles */
        .hero-bg {
            background-image: url('https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .hero-bg {
                background-attachment: scroll;
            }
            
            .mobile-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border-top: 1px solid #e5e7eb;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            
            .mobile-menu.active {
                display: block;
                animation: fadeInUp 0.3s ease-out;
            }
        }

        /* Grid layouts */
        .grid-1 { display: grid; grid-template-columns: 1fr; gap: 1rem; }
        .grid-2 { display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; }
        .grid-3 { display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; }
        .grid-4 { display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; }

        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .grid-3, .grid-4 { grid-template-columns: repeat(2, 1fr); }
        }

        /* Utilities */
        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .section-padding {
            padding: 5rem 0;
        }

        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }

        .mb-2 { margin-bottom: 0.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mb-8 { margin-bottom: 2rem; }
        .mb-12 { margin-bottom: 3rem; }
        .mb-16 { margin-bottom: 4rem; }

        .mt-2 { margin-top: 0.5rem; }
        .mt-4 { margin-top: 1rem; }
        .mt-6 { margin-top: 1.5rem; }
        .mt-8 { margin-top: 2rem; }
        .mt-12 { margin-top: 3rem; }
        .mt-16 { margin-top: 4rem; }

        .p-4 { padding: 1rem; }
        .p-6 { padding: 1.5rem; }
        .p-8 { padding: 2rem; }

        .px-4 { padding-left: 1rem; padding-right: 1rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .space-x-4 > * + * { margin-left: 1rem; }
        .space-y-2 > * + * { margin-top: 0.5rem; }
        .space-y-4 > * + * { margin-top: 1rem; }
        .space-y-6 > * + * { margin-top: 1.5rem; }

        .hidden { display: none; }
        .block { display: block; }
        .inline-block { display: inline-block; }

        @media (min-width: 768px) {
            .md\\:flex { display: flex; }
            .md\\:hidden { display: none; }
            .md\\:block { display: block; }
            .md\\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
            .md\\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
            .md\\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        }

        .relative { position: relative; }
        .absolute { position: absolute; }
        .fixed { position: fixed; }

        .top-0 { top: 0; }
        .left-0 { left: 0; }
        .right-0 { right: 0; }
        .bottom-0 { bottom: 0; }
        .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

        .z-10 { z-index: 10; }
        .z-50 { z-index: 50; }
        .z-100 { z-index: 100; }

        .w-full { width: 100%; }
        .h-full { height: 100%; }
        .min-h-screen { min-height: 100vh; }

        .object-cover { object-fit: cover; }
        .rounded { border-radius: 0.25rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .rounded-full { border-radius: 9999px; }

        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); }

        .bg-white { background-color: white; }
        .bg-gray-50 { background-color: #f9fafb; }
        .bg-gray-900 { background-color: #111827; }
        .bg-amber-50 { background-color: #fffbeb; }
        .bg-amber-100 { background-color: #fef3c7; }
        .bg-amber-600 { background-color: #d97706; }

        .text-white { color: white; }
        .text-gray-600 { color: #4b5563; }
        .text-gray-700 { color: #374151; }
        .text-gray-900 { color: #111827; }
        .text-amber-600 { color: #d97706; }
        .text-amber-400 { color: #fbbf24; }

        .border { border: 1px solid #e5e7eb; }
        .border-t { border-top: 1px solid #e5e7eb; }
        .border-amber-600 { border-color: #d97706; }

        .cursor-pointer { cursor: pointer; }
        .select-none { user-select: none; }

        .transition-all { transition: all 0.3s ease; }
        .transition-colors { transition: color 0.2s ease, background-color 0.2s ease; }
        .transition-transform { transition: transform 0.3s ease; }

        .hover\\:scale-105:hover { transform: scale(1.05); }
        .hover\\:bg-amber-700:hover { background-color: #b45309; }
        .hover\\:text-amber-600:hover { color: #d97706; }

        .transform { transform: translateZ(0); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav id="navigation" class="nav-sticky bg-white">
        <div class="container">
            <div class="flex justify-between items-center" style="height: 4rem;">
                <!-- Logo -->
                <div>
                    <h1 class="text-amber-600" style="font-size: 1.5rem; font-weight: 600; letter-spacing: 0.05em; margin: 0;">Élégant Catering</h1>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="nav-link text-gray-700 hover:text-amber-600 transition-colors">Home</a>
                    <a href="#about" class="nav-link text-gray-700 hover:text-amber-600 transition-colors">About</a>
                    <a href="#services" class="nav-link text-gray-700 hover:text-amber-600 transition-colors">Services</a>
                    <a href="#menu" class="nav-link text-gray-700 hover:text-amber-600 transition-colors">Menu</a>
                    <a href="#gallery" class="nav-link text-gray-700 hover:text-amber-600 transition-colors">Gallery</a>
                    <a href="#testimonials" class="nav-link text-gray-700 hover:text-amber-600 transition-colors">Testimonials</a>
                    <a href="#contact" class="nav-link text-gray-700 hover:text-amber-600 transition-colors">Contact</a>
                    
                    <div class="flex items-center space-x-4" style="margin-left: 2rem; padding-left: 2rem; border-left: 1px solid #e5e7eb;">
                        <div class="flex items-center space-x-1 text-gray-600">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span style="font-size: 0.875rem;">(*************</span>
                        </div>
                        <a href="#contact" class="btn btn-primary">Book Now</a>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-amber-600 transition-colors">
                        <svg id="menu-icon" width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                        <svg id="close-icon" width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: none;">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="mobile-menu">
            <div class="p-4 space-y-4">
                <a href="#home" class="nav-link block py-2 text-gray-700 hover:text-amber-600 transition-colors">Home</a>
                <a href="#about" class="nav-link block py-2 text-gray-700 hover:text-amber-600 transition-colors">About</a>
                <a href="#services" class="nav-link block py-2 text-gray-700 hover:text-amber-600 transition-colors">Services</a>
                <a href="#menu" class="nav-link block py-2 text-gray-700 hover:text-amber-600 transition-colors">Menu</a>
                <a href="#gallery" class="nav-link block py-2 text-gray-700 hover:text-amber-600 transition-colors">Gallery</a>
                <a href="#testimonials" class="nav-link block py-2 text-gray-700 hover:text-amber-600 transition-colors">Testimonials</a>
                <a href="#contact" class="nav-link block py-2 text-gray-700 hover:text-amber-600 transition-colors">Contact</a>
                <div class="py-2 border-t">
                    <div class="flex items-center space-x-2 text-gray-600 mb-2">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                        </svg>
                        <span style="font-size: 0.875rem;">(*************</span>
                    </div>
                    <a href="#contact" class="btn btn-primary w-full">Book Now</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-bg relative min-h-screen flex items-center justify-center">
        <div class="absolute inset-0 bg-black" style="opacity: 0.4;"></div>
        
        <div class="container relative z-10 text-center text-white">
            <div class="mb-8 animate-fade-in-up">
                <h1 class="mb-6 text-shadow" style="font-size: 3rem; line-height: 1.2; letter-spacing: 0.05em;">
                    <span class="block text-white">Élégant</span>
                    <span class="block text-amber-400">Catering</span>
                </h1>
                <p class="mb-8 text-shadow" style="font-size: 1.25rem; max-width: 48rem; margin-left: auto; margin-right: auto; color: #f3f4f6;">
                    Creating extraordinary culinary experiences for your most memorable occasions
                </p>
                
                <div class="flex flex-col md:flex-row gap-4 justify-center items-center mb-12">
                    <a href="#menu" class="btn btn-primary">View Our Menu</a>
                    <a href="#contact" class="btn btn-white">Get Quote</a>
                </div>
            </div>

            <!-- Stats -->
            <div class="grid-1 md:grid-cols-3 gap-8" style="max-width: 64rem; margin: 0 auto;">
                <div class="text-center">
                    <div class="bg-amber-600 p-4 rounded-full mb-4" style="width: 5rem; height: 5rem; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                        <svg width="32" height="32" fill="none" stroke="white" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                        </svg>
                    </div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">15+</div>
                    <div style="color: #d1d5db;">Years Experience</div>
                </div>
                
                <div class="text-center">
                    <div class="bg-amber-600 p-4 rounded-full mb-4" style="width: 5rem; height: 5rem; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                        <svg width="32" height="32" fill="none" stroke="white" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    </div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">500+</div>
                    <div style="color: #d1d5db;">Events Catered</div>
                </div>
                
                <div class="text-center">
                    <div class="bg-amber-600 p-4 rounded-full mb-4" style="width: 5rem; height: 5rem; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                        <svg width="32" height="32" fill="none" stroke="white" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                        </svg>
                    </div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">50+</div>
                    <div style="color: #d1d5db;">Awards Won</div>
                </div>
            </div>
        </div>

        <!-- Scroll indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
            <a href="#about" class="flex flex-col items-center hover:text-amber-400 transition-colors">
                <div class="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                    <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
                </div>
                <span style="font-size: 0.875rem; margin-top: 0.5rem;">Scroll Down</span>
            </a>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section-padding bg-gray-50">
        <div class="container">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 style="font-size: 3rem; margin-bottom: 1.5rem;">About Us</h2>
                <div class="w-24 h-1 bg-amber-600 mx-auto mb-8" style="width: 6rem; height: 0.25rem; margin: 0 auto 2rem auto;"></div>
                <p style="font-size: 1.25rem; color: #4b5563; max-width: 48rem; margin: 0 auto;">
                    With over 15 years of experience, we bring passion, creativity, and excellence to every event we cater.
                </p>
            </div>

            <!-- Story Section -->
            <div class="grid-1 md:grid-cols-2 gap-12 items-center mb-16">
                <div>
                    <h3 style="font-size: 2rem; margin-bottom: 1.5rem;">Our Story</h3>
                    <p class="text-gray-600 mb-6">
                        Founded in 2008 by Chef Maria Rodriguez, Élégant Catering began as a small family business with a big dream: 
                        to create unforgettable culinary experiences that bring people together. What started in a humble kitchen has 
                        grown into one of the city's most trusted catering companies.
                    </p>
                    <p class="text-gray-600 mb-6">
                        We believe that food is more than sustenance—it's a way to celebrate life's most precious moments. 
                        Every dish we create is crafted with the finest ingredients, attention to detail, and love for the culinary arts.
                    </p>
                    <div class="grid-2 gap-6 mt-8">
                        <div class="text-center p-4 bg-white rounded-lg shadow-lg">
                            <div style="font-size: 1.5rem; color: #d97706; margin-bottom: 0.5rem;">500+</div>
                            <div class="text-gray-600">Events</div>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg shadow-lg">
                            <div style="font-size: 1.5rem; color: #d97706; margin-bottom: 0.5rem;">15+</div>
                            <div class="text-gray-600">Years</div>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                         alt="Our kitchen and team" class="rounded-lg shadow-xl w-full h-full object-cover">
                </div>
            </div>

            <!-- Chef Profiles -->
            <div>
                <h3 class="text-center mb-12" style="font-size: 2rem;">Meet Our Chefs</h3>
                <div class="grid-1 md:grid-cols-3 gap-8">
                    <!-- Chef 1 -->
                    <div class="card hover:scale-105">
                        <img src="https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=984&q=80" 
                             alt="Chef Maria Rodriguez" class="w-full object-cover" style="height: 16rem;">
                        <div class="p-6">
                            <h4 style="font-size: 1.25rem; margin-bottom: 0.5rem;">Chef Maria Rodriguez</h4>
                            <p class="text-amber-600 mb-4">Executive Chef & Founder</p>
                            <p class="text-gray-600" style="font-size: 0.875rem;">
                                With 20+ years in fine dining, Maria brings international flavors and innovative techniques to every event.
                            </p>
                        </div>
                    </div>

                    <!-- Chef 2 -->
                    <div class="card hover:scale-105">
                        <img src="https://images.unsplash.com/photo-1607631568010-a87245c0daf8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80" 
                             alt="Chef David Kim" class="w-full object-cover" style="height: 16rem;">
                        <div class="p-6">
                            <h4 style="font-size: 1.25rem; margin-bottom: 0.5rem;">Chef David Kim</h4>
                            <p class="text-amber-600 mb-4">Pastry Chef</p>
                            <p class="text-gray-600" style="font-size: 0.875rem;">
                                David's artistic desserts are works of art that perfectly complement our savory offerings.
                            </p>
                        </div>
                    </div>

                    <!-- Chef 3 -->
                    <div class="card hover:scale-105">
                        <img src="https://images.unsplash.com/photo-1559339352-11d035aa65de?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80" 
                             alt="Chef Sofia Martinez" class="w-full object-cover" style="height: 16rem;">
                        <div class="p-6">
                            <h4 style="font-size: 1.25rem; margin-bottom: 0.5rem;">Chef Sofia Martinez</h4>
                            <p class="text-amber-600 mb-4">Sous Chef</p>
                            <p class="text-gray-600" style="font-size: 0.875rem;">
                                Sofia specializes in Mediterranean cuisine and ensures every dish meets our high standards.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Values -->
            <div class="mt-16">
                <h3 class="text-center mb-12" style="font-size: 2rem;">Our Values</h3>
                <div class="grid-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="bg-amber-100 rounded-full mb-4" style="width: 4rem; height: 4rem; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">🌟</div>
                        <h4 style="font-size: 1.25rem; margin-bottom: 1rem;">Excellence</h4>
                        <p class="text-gray-600">
                            We strive for perfection in every dish, every service, and every moment of your event.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="bg-amber-100 rounded-full mb-4" style="width: 4rem; height: 4rem; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">💚</div>
                        <h4 style="font-size: 1.25rem; margin-bottom: 1rem;">Sustainability</h4>
                        <p class="text-gray-600">
                            We source locally and sustainably, supporting our community and protecting our environment.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="bg-amber-100 rounded-full mb-4" style="width: 4rem; height: 4rem; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">🤝</div>
                        <h4 style="font-size: 1.25rem; margin-bottom: 1rem;">Partnership</h4>
                        <p class="text-gray-600">
                            We work closely with you to understand your vision and bring it to life perfectly.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="section-padding bg-white">
        <div class="container">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 style="font-size: 3rem; margin-bottom: 1.5rem;">Our Services</h2>
                <div class="w-24 h-1 bg-amber-600 mx-auto mb-8" style="width: 6rem; height: 0.25rem; margin: 0 auto 2rem auto;"></div>
                <p style="font-size: 1.25rem; color: #4b5563; max-width: 48rem; margin: 0 auto;">
                    We specialize in creating unforgettable experiences for every occasion, big or small.
                </p>
            </div>

            <!-- Services Grid -->
            <div class="grid-1 md:grid-cols-2 gap-12 mb-16">
                <!-- Wedding Catering -->
                <div class="card">
                    <img src="https://images.unsplash.com/photo-1519167758481-83f550bb49b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2098&q=80" 
                         alt="Wedding Catering" class="w-full object-cover" style="height: 16rem;">
                    <div class="p-8">
                        <div class="flex items-center mb-4">
                            <div class="bg-amber-600 text-white p-3 rounded-lg mr-4">
                                <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                </svg>
                            </div>
                            <h3 style="font-size: 1.5rem;">Wedding Catering</h3>
                        </div>
                        <p class="text-gray-600 mb-6">
                            Make your special day perfect with our elegant wedding catering services, featuring customizable menus and professional service.
                        </p>
                        
                        <div class="grid-1 md:grid-cols-2 gap-2 mb-6">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Custom menu design</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Professional service staff</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Elegant presentation</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Bridal party tastings</span>
                            </div>
                        </div>
                        
                        <a href="#contact" class="btn btn-outline">Get Quote</a>
                    </div>
                </div>

                <!-- Corporate Events -->
                <div class="card">
                    <img src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80" 
                         alt="Corporate Events" class="w-full object-cover" style="height: 16rem;">
                    <div class="p-8">
                        <div class="flex items-center mb-4">
                            <div class="bg-amber-600 text-white p-3 rounded-lg mr-4">
                                <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                            </div>
                            <h3 style="font-size: 1.5rem;">Corporate Events</h3>
                        </div>
                        <p class="text-gray-600 mb-6">
                            Impress clients and colleagues with our sophisticated corporate catering, perfect for meetings, conferences, and company celebrations.
                        </p>
                        
                        <div class="grid-1 md:grid-cols-2 gap-2 mb-6">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Business lunch menus</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Conference catering</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Networking events</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Executive dining</span>
                            </div>
                        </div>
                        
                        <a href="#contact" class="btn btn-outline">Get Quote</a>
                    </div>
                </div>

                <!-- Private Parties -->
                <div class="card">
                    <img src="https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                         alt="Private Parties" class="w-full object-cover" style="height: 16rem;">
                    <div class="p-8">
                        <div class="flex items-center mb-4">
                            <div class="bg-amber-600 text-white p-3 rounded-lg mr-4">
                                <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                </svg>
                            </div>
                            <h3 style="font-size: 1.5rem;">Private Parties</h3>
                        </div>
                        <p class="text-gray-600 mb-6">
                            Celebrate life's special moments with our personalized private party catering, tailored to your unique style and preferences.
                        </p>
                        
                        <div class="grid-1 md:grid-cols-2 gap-2 mb-6">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Birthday celebrations</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Anniversary parties</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Holiday gatherings</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Intimate dinners</span>
                            </div>
                        </div>
                        
                        <a href="#contact" class="btn btn-outline">Get Quote</a>
                    </div>
                </div>

                <!-- Special Occasions -->
                <div class="card">
                    <img src="https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80" 
                         alt="Special Occasions" class="w-full object-cover" style="height: 16rem;">
                    <div class="p-8">
                        <div class="flex items-center mb-4">
                            <div class="bg-amber-600 text-white p-3 rounded-lg mr-4">
                                <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                                </svg>
                            </div>
                            <h3 style="font-size: 1.5rem;">Special Occasions</h3>
                        </div>
                        <p class="text-gray-600 mb-6">
                            From graduations to retirement parties, we provide exceptional catering for all of life's milestone moments.
                        </p>
                        
                        <div class="grid-1 md:grid-cols-2 gap-2 mb-6">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Graduation parties</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Retirement celebrations</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Baby showers</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                                <span class="text-gray-600" style="font-size: 0.875rem;">Fundraising events</span>
                            </div>
                        </div>
                        
                        <a href="#contact" class="btn btn-outline">Get Quote</a>
                    </div>
                </div>
            </div>

            <!-- Additional Services -->
            <div class="text-center bg-amber-50 rounded-lg p-8">
                <h3 style="font-size: 1.5rem; margin-bottom: 1rem;">Additional Services</h3>
                <div class="grid-1 md:grid-cols-4 gap-6 text-center">
                    <div class="p-4">
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">🍷</div>
                        <h4 class="mb-2">Bar Service</h4>
                        <p class="text-gray-600" style="font-size: 0.875rem;">Professional bartenders and premium beverages</p>
                    </div>
                    <div class="p-4">
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎂</div>
                        <h4 class="mb-2">Custom Cakes</h4>
                        <p class="text-gray-600" style="font-size: 0.875rem;">Artisanal cakes and desserts for any occasion</p>
                    </div>
                    <div class="p-4">
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">🍽️</div>
                        <h4 class="mb-2">Equipment Rental</h4>
                        <p class="text-gray-600" style="font-size: 0.875rem;">Tables, chairs, linens, and serving equipment</p>
                    </div>
                    <div class="p-4">
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">👥</div>
                        <h4 class="mb-2">Event Planning</h4>
                        <p class="text-gray-600" style="font-size: 0.875rem;">Full-service event coordination and planning</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Menu Section -->
    <section id="menu" class="section-padding bg-gray-50">
        <div class="container">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 style="font-size: 3rem; margin-bottom: 1.5rem;">Our Menu</h2>
                <div class="w-24 h-1 bg-amber-600 mx-auto mb-8" style="width: 6rem; height: 0.25rem; margin: 0 auto 2rem auto;"></div>
                <p style="font-size: 1.25rem; color: #4b5563; max-width: 48rem; margin: 0 auto;">
                    Discover our carefully crafted dishes made with the finest ingredients and exceptional attention to detail.
                </p>
            </div>

            <!-- Category Filter -->
            <div class="flex flex-wrap justify-center gap-4 mb-12" id="menu-categories">
                <button class="menu-category-btn btn btn-primary" data-category="appetizers">
                    <span class="mr-2">🥗</span>Appetizers
                </button>
                <button class="menu-category-btn btn bg-white text-gray-700 hover:bg-amber-50 hover:text-amber-600 shadow-lg" data-category="entrees">
                    <span class="mr-2">🍽️</span>Entrées
                </button>
                <button class="menu-category-btn btn bg-white text-gray-700 hover:bg-amber-50 hover:text-amber-600 shadow-lg" data-category="desserts">
                    <span class="mr-2">🍰</span>Desserts
                </button>
                <button class="menu-category-btn btn bg-white text-gray-700 hover:bg-amber-50 hover:text-amber-600 shadow-lg" data-category="beverages">
                    <span class="mr-2">🥂</span>Beverages
                </button>
            </div>

            <!-- Menu Items -->
            <div id="menu-items" class="grid-1 md:grid-cols-4 gap-8">
                <!-- Menu items will be populated by JavaScript -->
            </div>

            <!-- Custom Menu Note -->
            <div class="mt-16 text-center bg-white rounded-lg p-8 shadow-lg">
                <h3 style="font-size: 1.5rem; margin-bottom: 1rem;">Custom Menu Planning</h3>
                <p class="text-gray-600 mb-6" style="max-width: 32rem; margin: 0 auto 1.5rem auto;">
                    Our chefs can create custom menus tailored to your event's specific needs, dietary restrictions, 
                    and preferences. Contact us to discuss your vision and let us bring it to life.
                </p>
                <a href="#contact" class="btn btn-primary">Request Custom Menu</a>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="section-padding bg-white">
        <div class="container">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 style="font-size: 3rem; margin-bottom: 1.5rem;">Gallery</h2>
                <div class="w-24 h-1 bg-amber-600 mx-auto mb-8" style="width: 6rem; height: 0.25rem; margin: 0 auto 2rem auto;"></div>
                <p style="font-size: 1.25rem; color: #4b5563; max-width: 48rem; margin: 0 auto;">
                    Take a look at our recent events and culinary creations that showcase our commitment to excellence.
                </p>
            </div>

            <!-- Gallery Grid -->
            <div class="grid-1 md:grid-cols-4 gap-4" id="gallery-grid">
                <!-- Gallery images will be populated by JavaScript -->
            </div>

            <!-- Stats Section -->
            <div class="mt-16 grid-1 md:grid-cols-4 gap-8 text-center">
                <div class="p-6">
                    <div style="font-size: 2rem; color: #d97706; margin-bottom: 0.5rem;">500+</div>
                    <div class="text-gray-600">Events Catered</div>
                </div>
                <div class="p-6">
                    <div style="font-size: 2rem; color: #d97706; margin-bottom: 0.5rem;">50K+</div>
                    <div class="text-gray-600">Guests Served</div>
                </div>
                <div class="p-6">
                    <div style="font-size: 2rem; color: #d97706; margin-bottom: 0.5rem;">15+</div>
                    <div class="text-gray-600">Years Experience</div>
                </div>
                <div class="p-6">
                    <div style="font-size: 2rem; color: #d97706; margin-bottom: 0.5rem;">98%</div>
                    <div class="text-gray-600">Client Satisfaction</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Modal -->
    <div id="gallery-modal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeGalleryModal()">&times;</span>
            <img id="modal-image" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
        </div>
    </div>

    <!-- Testimonials Section -->
    <section id="testimonials" class="section-padding bg-amber-50">
        <div class="container">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 style="font-size: 3rem; margin-bottom: 1.5rem;">What Our Clients Say</h2>
                <div class="w-24 h-1 bg-amber-600 mx-auto mb-8" style="width: 6rem; height: 0.25rem; margin: 0 auto 2rem auto;"></div>
                <p style="font-size: 1.25rem; color: #4b5563; max-width: 48rem; margin: 0 auto;">
                    Don't just take our word for it. Here's what our satisfied clients have to say about their experience with us.
                </p>
            </div>

            <!-- Testimonial Carousel -->
            <div class="relative" style="max-width: 64rem; margin: 0 auto;">
                <div id="testimonial-content" class="bg-white rounded-lg shadow-xl p-8 md:p-12 text-center">
                    <!-- Testimonial content will be populated by JavaScript -->
                </div>

                <!-- Navigation Buttons -->
                <button id="prev-testimonial" class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all hover:bg-amber-50">
                    <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                
                <button id="next-testimonial" class="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all hover:bg-amber-50">
                    <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>

            <!-- Dots Indicator -->
            <div id="testimonial-dots" class="flex justify-center mt-8 space-x-2">
                <!-- Dots will be populated by JavaScript -->
            </div>

            <!-- Review Summary -->
            <div class="mt-16 grid-1 md:grid-cols-3 gap-8 text-center">
                <div class="bg-white rounded-lg p-6 shadow-lg">
                    <div style="font-size: 2rem; color: #d97706; margin-bottom: 0.5rem;">98%</div>
                    <div class="text-gray-600">Client Satisfaction</div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-lg">
                    <div style="font-size: 2rem; color: #d97706; margin-bottom: 0.5rem;">4.9/5</div>
                    <div class="text-gray-600">Average Rating</div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-lg">
                    <div style="font-size: 2rem; color: #d97706; margin-bottom: 0.5rem;">95%</div>
                    <div class="text-gray-600">Repeat Clients</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section-padding bg-gray-50">
        <div class="container">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 style="font-size: 3rem; margin-bottom: 1.5rem;">Contact Us</h2>
                <div class="w-24 h-1 bg-amber-600 mx-auto mb-8" style="width: 6rem; height: 0.25rem; margin: 0 auto 2rem auto;"></div>
                <p style="font-size: 1.25rem; color: #4b5563; max-width: 48rem; margin: 0 auto;">
                    Ready to make your event unforgettable? Get in touch with us to discuss your catering needs.
                </p>
            </div>

            <div class="grid-1 md:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div class="bg-white rounded-lg shadow-xl p-8">
                    <h3 style="font-size: 1.5rem; margin-bottom: 1.5rem;">Get a Quote</h3>
                    <form id="contact-form" class="space-y-6">
                        <div class="grid-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">Name *</label>
                                <input type="text" name="name" class="form-input" required>
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">Email *</label>
                                <input type="email" name="email" class="form-input" required>
                            </div>
                        </div>

                        <div class="grid-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">Phone</label>
                                <input type="tel" name="phone" class="form-input">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">Event Date</label>
                                <input type="date" name="eventDate" class="form-input">
                            </div>
                        </div>

                        <div class="grid-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">Event Type</label>
                                <select name="eventType" class="form-select">
                                    <option value="">Select event type</option>
                                    <option value="wedding">Wedding</option>
                                    <option value="corporate">Corporate Event</option>
                                    <option value="private">Private Party</option>
                                    <option value="special">Special Occasion</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">Number of Guests</label>
                                <select name="guestCount" class="form-select">
                                    <option value="">Select guest count</option>
                                    <option value="10-25">10-25 guests</option>
                                    <option value="26-50">26-50 guests</option>
                                    <option value="51-100">51-100 guests</option>
                                    <option value="101-200">101-200 guests</option>
                                    <option value="200+">200+ guests</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-2">Message</label>
                            <textarea name="message" class="form-textarea" placeholder="Tell us about your event, dietary requirements, or any special requests..."></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary w-full flex items-center justify-center">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="mr-2">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                            Send Inquiry
                        </button>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="space-y-8">
                    <!-- Contact Details -->
                    <div class="bg-white rounded-lg shadow-xl p-8">
                        <h3 style="font-size: 1.5rem; margin-bottom: 1.5rem;">Get in Touch</h3>
                        
                        <div class="space-y-6">
                            <div class="flex items-start">
                                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-amber-600 mr-4 mt-1">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                </svg>
                                <div>
                                    <h4 class="mb-1">Phone</h4>
                                    <p class="text-gray-600">(*************</p>
                                    <p class="text-gray-500" style="font-size: 0.875rem;">Mon-Fri 9AM-6PM</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-amber-600 mr-4 mt-1">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                                <div>
                                    <h4 class="mb-1">Email</h4>
                                    <p class="text-gray-600"><EMAIL></p>
                                    <p class="text-gray-500" style="font-size: 0.875rem;">We'll respond within 24 hours</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-amber-600 mr-4 mt-1">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <div>
                                    <h4 class="mb-1">Address</h4>
                                    <p class="text-gray-600">123 Culinary Street<br>Food District, NYC 10001</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-amber-600 mr-4 mt-1">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                                <div>
                                    <h4 class="mb-1">Business Hours</h4>
                                    <p class="text-gray-600">
                                        Mon-Fri: 9:00 AM - 6:00 PM<br>
                                        Sat: 10:00 AM - 4:00 PM<br>
                                        Sun: By appointment
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media & Map -->
                    <div class="bg-white rounded-lg shadow-xl p-8">
                        <h3 style="font-size: 1.5rem; margin-bottom: 1.5rem;">Follow Us</h3>
                        
                        <div class="flex space-x-4 mb-6">
                            <a href="https://facebook.com" target="_blank" class="bg-amber-100 p-3 rounded-full hover:bg-amber-200 transition-colors">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="text-amber-600">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                            <a href="https://instagram.com" target="_blank" class="bg-amber-100 p-3 rounded-full hover:bg-amber-200 transition-colors">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="text-amber-600">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.99-2.448-2.337 0-1.297.99-2.448 2.337-2.448 1.297 0 2.448.99 2.448 2.337 0 1.297-.99 2.448-2.337 2.448zm7.12 0c-1.297 0-2.448-.99-2.448-2.337 0-1.297.99-2.448 2.337-2.448 1.297 0 2.448.99 2.448 2.337 0 1.297-.99 2.448-2.337 2.448z"/>
                                </svg>
                            </a>
                            <a href="https://twitter.com" target="_blank" class="bg-amber-100 p-3 rounded-full hover:bg-amber-200 transition-colors">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24" class="text-amber-600">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                        </div>

                        <!-- Map Placeholder -->
                        <div class="bg-gray-200 rounded-lg flex items-center justify-center" style="height: 16rem;">
                            <div class="text-center text-gray-500">
                                <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="mx-auto mb-2">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <p>Interactive Map</p>
                                <p style="font-size: 0.875rem;">123 Culinary Street, NYC</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white" style="padding: 4rem 0;">
        <div class="container">
            <div class="grid-1 md:grid-cols-4 gap-8 mb-8">
                <!-- Company Info -->
                <div>
                    <h3 class="text-amber-400 mb-4" style="font-size: 1.5rem;">Élégant Catering</h3>
                    <p class="text-gray-400 mb-4">
                        Creating extraordinary culinary experiences for your most memorable occasions since 2008.
                    </p>
                    <div class="flex space-x-3">
                        <a href="https://facebook.com" target="_blank" class="bg-gray-800 p-2 rounded-full hover:bg-amber-600 transition-colors">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="https://instagram.com" target="_blank" class="bg-gray-800 p-2 rounded-full hover:bg-amber-600 transition-colors">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.99-2.448-2.337 0-1.297.99-2.448 2.337-2.448 1.297 0 2.448.99 2.448 2.337 0 1.297-.99 2.448-2.337 2.448zm7.12 0c-1.297 0-2.448-.99-2.448-2.337 0-1.297.99-2.448 2.337-2.448 1.297 0 2.448.99 2.448 2.337 0 1.297-.99 2.448-2.337 2.448z"/>
                            </svg>
                        </a>
                        <a href="https://twitter.com" target="_blank" class="bg-gray-800 p-2 rounded-full hover:bg-amber-600 transition-colors">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 style="font-size: 1.125rem; margin-bottom: 1rem;">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-400 hover:text-amber-400 transition-colors">Home</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-amber-400 transition-colors">About Us</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-amber-400 transition-colors">Services</a></li>
                        <li><a href="#menu" class="text-gray-400 hover:text-amber-400 transition-colors">Menu</a></li>
                        <li><a href="#gallery" class="text-gray-400 hover:text-amber-400 transition-colors">Gallery</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div>
                    <h4 style="font-size: 1.125rem; margin-bottom: 1rem;">Services</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>Wedding Catering</li>
                        <li>Corporate Events</li>
                        <li>Private Parties</li>
                        <li>Special Occasions</li>
                        <li>Event Planning</li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 style="font-size: 1.125rem; margin-bottom: 1rem;">Contact Info</h4>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-amber-400 mr-3 mt-0.5">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <p class="text-gray-400">(*************</p>
                        </div>
                        <div class="flex items-start">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-amber-400 mr-3 mt-0.5">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <p class="text-gray-400"><EMAIL></p>
                        </div>
                        <div class="flex items-start">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-amber-400 mr-3 mt-0.5">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <p class="text-gray-400">123 Culinary Street<br>Food District, NYC 10001</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 mb-4 md:mb-0" style="font-size: 0.875rem;">
                        © 2024 Élégant Catering. All rights reserved.
                    </div>
                    <div class="text-gray-400 flex items-center" style="font-size: 0.875rem;">
                        Made with <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" class="text-red-500 mx-1"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/></svg> for great food
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Data for menu items
        const menuItems = {
            appetizers: [
                {
                    name: "Truffle Arancini",
                    description: "Crispy risotto balls with truffle oil and parmesan",
                    price: "$12",
                    image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1081&q=80"
                },
                {
                    name: "Smoked Salmon Canapés",
                    description: "Fresh dill cream cheese on cucumber rounds",
                    price: "$15",
                    image: "https://images.unsplash.com/photo-1544378968-7d4c2c2e6d6e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80"
                },
                {
                    name: "Beef Wellington Bites",
                    description: "Mini beef wellington with mushroom duxelles",
                    price: "$18",
                    image: "https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80"
                },
                {
                    name: "Caprese Skewers",
                    description: "Fresh mozzarella, basil, and cherry tomatoes",
                    price: "$10",
                    image: "https://images.unsplash.com/photo-1608897013039-887f21d8c804?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2044&q=80"
                }
            ],
            entrees: [
                {
                    name: "Herb-Crusted Rack of Lamb",
                    description: "New Zealand lamb with rosemary and garlic crust",
                    price: "$42",
                    image: "https://images.unsplash.com/photo-1558030006-450675393462?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1031&q=80"
                },
                {
                    name: "Pan-Seared Salmon",
                    description: "Atlantic salmon with lemon butter sauce",
                    price: "$35",
                    image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80"
                },
                {
                    name: "Filet Mignon",
                    description: "8oz tenderloin with red wine reduction",
                    price: "$48",
                    image: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                },
                {
                    name: "Vegetarian Risotto",
                    description: "Wild mushroom risotto with truffle oil",
                    price: "$28",
                    image: "https://images.unsplash.com/photo-1476124369491-e7addf5db371?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                }
            ],
            desserts: [
                {
                    name: "Chocolate Lava Cake",
                    description: "Warm chocolate cake with vanilla bean ice cream",
                    price: "$12",
                    image: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1587&q=80"
                },
                {
                    name: "Tiramisu",
                    description: "Classic Italian dessert with espresso and mascarpone",
                    price: "$10",
                    image: "https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
                },
                {
                    name: "Crème Brûlée",
                    description: "Vanilla custard with caramelized sugar crust",
                    price: "$11",
                    image: "https://images.unsplash.com/photo-1470324161839-ce2bb6fa6bc3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
                },
                {
                    name: "Fresh Berry Tart",
                    description: "Seasonal berries with pastry cream",
                    price: "$9",
                    image: "https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2059&q=80"
                }
            ],
            beverages: [
                {
                    name: "Signature Cocktails",
                    description: "Custom cocktails crafted by our mixologists",
                    price: "$14",
                    image: "https://images.unsplash.com/photo-1514362545857-3bc16c4c7d1b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
                },
                {
                    name: "Wine Selection",
                    description: "Curated wines from around the world",
                    price: "$12-45",
                    image: "https://images.unsplash.com/photo-1510812431401-41d2bd2722f3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
                },
                {
                    name: "Craft Beer",
                    description: "Local and imported craft beer selection",
                    price: "$8",
                    image: "https://images.unsplash.com/photo-1608270586620-248524c67de9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
                },
                {
                    name: "Fresh Juices",
                    description: "Freshly pressed seasonal fruit juices",
                    price: "$6",
                    image: "https://images.unsplash.com/photo-1622597467836-f3285f2131b8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
                }
            ]
        };

        // Gallery images data
        const galleryImages = [
            {
                src: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
                alt: "Elegant wedding setup",
                category: "Wedding"
            },
            {
                src: "https://images.unsplash.com/photo-1555244162-803834f70033?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
                alt: "Gourmet appetizers",
                category: "Food"
            },
            {
                src: "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
                alt: "Corporate event setup",
                category: "Corporate"
            },
            {
                src: "https://images.unsplash.com/photo-1558030006-450675393462?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1031&q=80",
                alt: "Herb-crusted lamb",
                category: "Food"
            },
            {
                src: "https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
                alt: "Birthday party celebration",
                category: "Private Party"
            },
            {
                src: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1587&q=80",
                alt: "Chocolate lava cake",
                category: "Desserts"
            },
            {
                src: "https://images.unsplash.com/photo-1519167758481-83f550bb49b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2098&q=80",
                alt: "Wedding reception",
                category: "Wedding"
            },
            {
                src: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80",
                alt: "Pan-seared salmon",
                category: "Food"
            },
            {
                src: "https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2059&q=80",
                alt: "Berry tarts",
                category: "Desserts"
            },
            {
                src: "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
                alt: "Special occasion setup",
                category: "Events"
            },
            {
                src: "https://images.unsplash.com/photo-1514362545857-3bc16c4c7d1b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
                alt: "Cocktail service",
                category: "Beverages"
            },
            {
                src: "https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
                alt: "Tiramisu dessert",
                category: "Desserts"
            }
        ];

        // Testimonials data
        const testimonials = [
            {
                name: "Sarah Johnson",
                role: "Bride",
                event: "Wedding Reception",
                rating: 5,
                text: "Élégant Catering made our wedding day absolutely perfect! The food was exquisite, the presentation was beautiful, and the service was flawless. Our guests are still raving about the meal months later.",
                image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80"
            },
            {
                name: "Michael Chen",
                role: "CEO",
                event: "Corporate Gala",
                rating: 5,
                text: "We've used Élégant Catering for multiple corporate events, and they never disappoint. Their attention to detail and professional service always impresses our clients and employees.",
                image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
            },
            {
                name: "Emily Rodriguez",
                role: "Event Planner",
                event: "Anniversary Celebration",
                rating: 5,
                text: "As an event planner, I've worked with many caterers, but Élégant stands out. Their creativity, reliability, and exceptional cuisine make every event memorable. Highly recommended!",
                image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80"
            },
            {
                name: "David Thompson",
                role: "Birthday Host",
                event: "50th Birthday Party",
                rating: 5,
                text: "The team at Élégant Catering went above and beyond for my 50th birthday party. From the initial consultation to the cleanup, everything was handled with professionalism and care.",
                image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80"
            },
            {
                name: "Lisa Park",
                role: "Mother of Bride",
                event: "Bridal Shower",
                rating: 5,
                text: "Maria and her team created the most beautiful bridal shower for my daughter. Every detail was perfect, and the food was absolutely delicious. Thank you for making it so special!",
                image: "https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80"
            }
        ];

        // Current states
        let currentMenuCategory = 'appetizers';
        let currentTestimonial = 0;
        let testimonialInterval;

        // DOM ready
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            initializeMenu();
            initializeGallery();
            initializeTestimonials();
            initializeContactForm();
            initializeScrollEffects();
        });

        // Navigation functionality
        function initializeNavigation() {
            const nav = document.getElementById('navigation');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');
            const closeIcon = document.getElementById('close-icon');
            const navLinks = document.querySelectorAll('.nav-link');

            // Scroll effect for navigation
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    nav.classList.add('nav-scrolled');
                } else {
                    nav.classList.remove('nav-scrolled');
                }
            });

            // Mobile menu toggle
            mobileMenuBtn.addEventListener('click', function() {
                mobileMenu.classList.toggle('active');
                if (mobileMenu.classList.contains('active')) {
                    menuIcon.style.display = 'none';
                    closeIcon.style.display = 'block';
                } else {
                    menuIcon.style.display = 'block';
                    closeIcon.style.display = 'none';
                }
            });

            // Close mobile menu when clicking nav links
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileMenu.classList.remove('active');
                    menuIcon.style.display = 'block';
                    closeIcon.style.display = 'none';
                });
            });
        }

        // Menu functionality
        function initializeMenu() {
            const categoryBtns = document.querySelectorAll('.menu-category-btn');
            const menuItemsContainer = document.getElementById('menu-items');

            // Category button click handlers
            categoryBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const category = this.dataset.category;
                    
                    // Update active button
                    categoryBtns.forEach(b => {
                        b.className = 'menu-category-btn btn bg-white text-gray-700 hover:bg-amber-50 hover:text-amber-600 shadow-lg';
                    });
                    this.className = 'menu-category-btn btn btn-primary';
                    
                    // Update menu items
                    currentMenuCategory = category;
                    displayMenuItems();
                });
            });

            // Initial menu display
            displayMenuItems();
        }

        function displayMenuItems() {
            const menuItemsContainer = document.getElementById('menu-items');
            const items = menuItems[currentMenuCategory];
            
            menuItemsContainer.innerHTML = items.map(item => `
                <div class="card hover:scale-105">
                    <img src="${item.image}" alt="${item.name}" class="w-full object-cover" style="height: 12rem;">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-3">
                            <h3 style="font-size: 1.125rem;">${item.name}</h3>
                            <span class="text-amber-600 ml-2">${item.price}</span>
                        </div>
                        <p class="text-gray-600" style="font-size: 0.875rem;">${item.description}</p>
                    </div>
                </div>
            `).join('');
        }

        // Gallery functionality
        function initializeGallery() {
            const galleryGrid = document.getElementById('gallery-grid');
            
            galleryGrid.innerHTML = galleryImages.map((image, index) => `
                <div class="relative group cursor-pointer overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all hover:scale-105" onclick="openGalleryModal('${image.src}', '${image.alt}')">
                    <img src="${image.src}" alt="${image.alt}" class="w-full object-cover transition-transform group-hover:scale-110" style="height: 16rem;">
                    <div class="absolute inset-0 bg-black opacity-0 group-hover:opacity-40 transition-opacity flex items-center justify-center">
                        <div class="text-center text-white">
                            <p style="font-size: 0.875rem; font-weight: 500;">${image.category}</p>
                            <p style="font-size: 0.75rem; margin-top: 0.25rem;">${image.alt}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function openGalleryModal(src, alt) {
            const modal = document.getElementById('gallery-modal');
            const modalImage = document.getElementById('modal-image');
            
            modalImage.src = src;
            modalImage.alt = alt;
            modal.classList.add('active');
        }

        function closeGalleryModal() {
            const modal = document.getElementById('gallery-modal');
            modal.classList.remove('active');
        }

        // Close modal when clicking outside
        document.getElementById('gallery-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeGalleryModal();
            }
        });

        // Testimonials functionality
        function initializeTestimonials() {
            displayTestimonial();
            createTestimonialDots();
            
            // Auto-rotate testimonials
            testimonialInterval = setInterval(nextTestimonial, 6000);
            
            // Navigation buttons
            document.getElementById('prev-testimonial').addEventListener('click', prevTestimonial);
            document.getElementById('next-testimonial').addEventListener('click', nextTestimonial);
        }

        function displayTestimonial() {
            const testimonial = testimonials[currentTestimonial];
            const content = document.getElementById('testimonial-content');
            
            content.innerHTML = `
                <div class="mb-8">
                    <img src="${testimonial.image}" alt="${testimonial.name}" class="rounded-full mx-auto object-cover shadow-lg" style="width: 5rem; height: 5rem;">
                </div>

                <div class="flex justify-center mb-6">
                    ${Array.from({length: 5}, (_, i) => `
                        <svg width="20" height="20" fill="${i < testimonial.rating ? 'currentColor' : 'none'}" stroke="currentColor" viewBox="0 0 24 24" class="${i < testimonial.rating ? 'text-amber-400' : 'text-gray-300'}">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                        </svg>
                    `).join('')}
                </div>

                <blockquote class="text-gray-700 italic mb-8" style="font-size: 1.25rem;">
                    "${testimonial.text}"
                </blockquote>

                <div class="text-center">
                    <h4 style="font-size: 1.125rem; margin-bottom: 0.25rem;">
                        ${testimonial.name}
                    </h4>
                    <p class="text-amber-600 mb-1">
                        ${testimonial.role}
                    </p>
                    <p class="text-gray-500" style="font-size: 0.875rem;">
                        ${testimonial.event}
                    </p>
                </div>
            `;
            
            updateTestimonialDots();
        }

        function createTestimonialDots() {
            const dotsContainer = document.getElementById('testimonial-dots');
            dotsContainer.innerHTML = testimonials.map((_, index) => `
                <button class="testimonial-dot w-3 h-3 rounded-full transition-all ${index === currentTestimonial ? 'bg-amber-600 w-8' : 'bg-gray-300 hover:bg-amber-300'}" onclick="goToTestimonial(${index})"></button>
            `).join('');
        }

        function updateTestimonialDots() {
            const dots = document.querySelectorAll('.testimonial-dot');
            dots.forEach((dot, index) => {
                if (index === currentTestimonial) {
                    dot.className = 'testimonial-dot w-8 h-3 rounded-full transition-all bg-amber-600';
                } else {
                    dot.className = 'testimonial-dot w-3 h-3 rounded-full transition-all bg-gray-300 hover:bg-amber-300';
                }
            });
        }

        function nextTestimonial() {
            currentTestimonial = (currentTestimonial + 1) % testimonials.length;
            displayTestimonial();
            resetTestimonialInterval();
        }

        function prevTestimonial() {
            currentTestimonial = (currentTestimonial - 1 + testimonials.length) % testimonials.length;
            displayTestimonial();
            resetTestimonialInterval();
        }

        function goToTestimonial(index) {
            currentTestimonial = index;
            displayTestimonial();
            resetTestimonialInterval();
        }

        function resetTestimonialInterval() {
            clearInterval(testimonialInterval);
            testimonialInterval = setInterval(nextTestimonial, 6000);
        }

        // Contact form functionality
        function initializeContactForm() {
            const form = document.getElementById('contact-form');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                // Simulate form submission
                alert('Thank you for your inquiry! We will contact you within 24 hours.');
                
                // Reset form
                form.reset();
            });
        }

        // Scroll effects
        function initializeScrollEffects() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in-up');
                    }
                });
            }, observerOptions);

            // Observe sections for animations
            document.querySelectorAll('section').forEach(section => {
                observer.observe(section);
            });
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            // Close modal with Escape key
            if (e.key === 'Escape') {
                closeGalleryModal();
            }
            
            // Testimonial navigation with arrow keys
            if (e.key === 'ArrowLeft') {
                prevTestimonial();
            } else if (e.key === 'ArrowRight') {
                nextTestimonial();
            }
        });
    </script>
</body>
</html>