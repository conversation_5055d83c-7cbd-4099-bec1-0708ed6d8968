import { ChefHat, Award, Users } from 'lucide-react';
import { Button } from './ui/button';
import { ImageWithFallback } from './figma/ImageWithFallback';

export function HeroSection() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <ImageWithFallback
          src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
          alt="Elegant catering setup"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/40"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
        <div className="mb-8">
          <h1 className="text-5xl md:text-7xl mb-6 tracking-wide">
            <span className="block text-white">Élégant</span>
            <span className="block text-amber-400">Catering</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed text-gray-100">
            Creating extraordinary culinary experiences for your most memorable occasions
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button 
              onClick={() => scrollToSection('menu')}
              className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              View Our Menu
            </Button>
            <Button 
              onClick={() => scrollToSection('contact')}
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              Get Quote
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="flex flex-col items-center">
            <div className="bg-amber-600 p-4 rounded-full mb-4">
              <ChefHat className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl mb-2">15+</div>
            <div className="text-gray-200">Years Experience</div>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="bg-amber-600 p-4 rounded-full mb-4">
              <Users className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl mb-2">500+</div>
            <div className="text-gray-200">Events Catered</div>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="bg-amber-600 p-4 rounded-full mb-4">
              <Award className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl mb-2">50+</div>
            <div className="text-gray-200">Awards Won</div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <button 
          onClick={() => scrollToSection('about')}
          className="flex flex-col items-center hover:text-amber-400 transition-colors duration-200"
        >
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
          <span className="text-sm mt-2">Scroll Down</span>
        </button>
      </div>
    </section>
  );
}