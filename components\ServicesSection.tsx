import { Heart, Building2, Users, Utensils } from 'lucide-react';
import { ImageWithFallback } from './figma/ImageWithFallback';
import { Button } from './ui/button';

export function ServicesSection() {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const services = [
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Wedding Catering",
      description: "Make your special day perfect with our elegant wedding catering services, featuring customizable menus and professional service.",
      image: "https://images.unsplash.com/photo-1519167758481-83f550bb49b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2098&q=80",
      features: ["Custom menu design", "Professional service staff", "Elegant presentation", "Bridal party tastings"]
    },
    {
      icon: <Building2 className="w-8 h-8" />,
      title: "Corporate Events",
      description: "Impress clients and colleagues with our sophisticated corporate catering, perfect for meetings, conferences, and company celebrations.",
      image: "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
      features: ["Business lunch menus", "Conference catering", "Networking events", "Executive dining"]
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Private Parties",
      description: "Celebrate life's special moments with our personalized private party catering, tailored to your unique style and preferences.",
      image: "https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      features: ["Birthday celebrations", "Anniversary parties", "Holiday gatherings", "Intimate dinners"]
    },
    {
      icon: <Utensils className="w-8 h-8" />,
      title: "Special Occasions",
      description: "From graduations to retirement parties, we provide exceptional catering for all of life's milestone moments.",
      image: "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
      features: ["Graduation parties", "Retirement celebrations", "Baby showers", "Fundraising events"]
    }
  ];

  return (
    <section id="services" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl text-gray-900 mb-6">Our Services</h2>
          <div className="w-24 h-1 bg-amber-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We specialize in creating unforgettable experiences for every occasion, big or small.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {services.map((service, index) => (
            <div key={index} className="bg-gray-50 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
              <ImageWithFallback
                src={service.image}
                alt={service.title}
                className="w-full h-64 object-cover"
              />
              <div className="p-8">
                <div className="flex items-center mb-4">
                  <div className="bg-amber-600 text-white p-3 rounded-lg mr-4">
                    {service.icon}
                  </div>
                  <h3 className="text-2xl text-gray-900">{service.title}</h3>
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <div className="w-2 h-2 bg-amber-600 rounded-full mr-3"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <Button 
                  onClick={scrollToContact}
                  variant="outline"
                  className="border-amber-600 text-amber-600 hover:bg-amber-600 hover:text-white transition-colors duration-200"
                >
                  Get Quote
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Services */}
        <div className="text-center bg-amber-50 rounded-lg p-8">
          <h3 className="text-2xl mb-4 text-gray-900">Additional Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div className="p-4">
              <div className="text-3xl mb-2">🍷</div>
              <h4 className="mb-2 text-gray-900">Bar Service</h4>
              <p className="text-gray-600 text-sm">Professional bartenders and premium beverages</p>
            </div>
            <div className="p-4">
              <div className="text-3xl mb-2">🎂</div>
              <h4 className="mb-2 text-gray-900">Custom Cakes</h4>
              <p className="text-gray-600 text-sm">Artisanal cakes and desserts for any occasion</p>
            </div>
            <div className="p-4">
              <div className="text-3xl mb-2">🍽️</div>
              <h4 className="mb-2 text-gray-900">Equipment Rental</h4>
              <p className="text-gray-600 text-sm">Tables, chairs, linens, and serving equipment</p>
            </div>
            <div className="p-4">
              <div className="text-3xl mb-2">👥</div>
              <h4 className="mb-2 text-gray-900">Event Planning</h4>
              <p className="text-gray-600 text-sm">Full-service event coordination and planning</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}